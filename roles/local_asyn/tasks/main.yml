---
- name: 安装必要的系统包
  dnf:
    name:
      - git
      - python39
      - python39-pip
      - python39-devel
      - libvirt-devel
      - libvirt-libs
      - pkg-config
      - gcc
      - gcc-c++
      - glib2-devel
    state: present
    update_cache: yes

- name: 更新 glib2 到最新版本
  dnf:
    name:
      - glib2
      - glib2-devel
    state: latest

- name: 创建项目基础目录
  file:
    path: "{{ base_dir }}"
    state: directory
    mode: '0755'

- name: 从Git仓库拉取代码
  git:
    repo: "{{ item.git_repo }}"
    dest: "{{ base_dir }}/{{ item.name }}"
    version: "{{ item.version }}"
    force: yes
  loop: "{{ repos }}"

- name: 创建配置目录
  file:
    path: "{{ base_dir }}/hci_asyn/config"
    state: directory
    mode: '0755'

- name: 生成配置文件
  template:
    src: "settings.py.j2"
    dest: "{{ base_dir }}/hci_asyn/config/settings.py"
    mode: '0644'

- name: 创建虚拟环境
  shell: |
    python3.9 -m venv {{ python_dir }}/venv

- name: 安装依赖包
  shell: |
    source {{ python_dir }}/venv/bin/activate
    pip install -r {{ base_dir }}/hci_asyn/requirements.txt

- name: 创建 systemd 服务文件
  template:
    src: "local-asyn.service.j2"
    dest: "/etc/systemd/system/local-asyn.service"
    mode: '0644'
  notify: reload systemd

- name: 重新加载 systemd
  systemd:
    daemon_reload: yes

- name: 启动并启用 local-asyn 服务
  systemd:
    name: local-asyn
    state: started
    enabled: yes
