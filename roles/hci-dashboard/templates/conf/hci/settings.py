# asyn配置
MQ_TYPE="redis"
# MQ_TYPE="rabbitmq"
REDIS_URL = "redis://{{ vip }}:6379/0"
AMQP_URI = "amqp://admin:admin123@{{ vip }}//"
MSG_AMQP_URI = "amqp://admin:admin123@{{ vip }}//"
QUEUE_NAME = "hci_web"

# theweb配置
ETCD_HOST = 'localhost'

REDIS_HOSTNAME = "redis"
REDIS_PWD = ""
REDIS_PORT = "6379"


THESC_URI="http://127.0.0.1:9002"


RECYCLE_DAYS = 10

# DRIVER = "mysql"
# DB_USERNAME = "root"
# DB_PASSWORD = "newroot"
# DB_HOSTNAME = "127.0.0.1"
# DB_PORT = "3306"
# DB_DATABASE = "hci_db"

#对接的驱动有 mysql kingbase dqlite
# DRIVER = "kingbase"
# DB_USERNAME = "kingbase"
# DB_PASSWORD = "123456"
# DB_HOSTNAME = "127.0.0.1"
# #DB_HOSTNAME = "{{ vip }}"
# DB_PORT = "4321"
# DB_DATABASE = "hci_db"


DRIVER = "mysql"
# DB_NODES = "{{ vip }}:9001,***************:9001,***************:9001"
DB_USERNAME = "root"
DB_PASSWORD = "thecloud2015.1"
DB_HOSTNAME = "{{ vip }}"
DB_PORT = "3306"
DB_DATABASE = "hci_db"


#LOG_PATH = "/var/log/the/theweb.log"
LOG_PATH = "/tmp/theweb.log"
LOG_UPLOAD_PATH = "/tmp/theupload.log"
LOG_ASYN_PATH = "/tmp/theasyn.log"
LOG_USER_PATH = "/tmp/theuser.log"

JSON_LOG_PATH = "/var/log/the/hci_web.log"
JSON_LOG_UPLOAD_PATH = "/var/log/the/hci_upload.log"
JSON_LOG_ASYN_PATH = "/var/log/the/hci_asyn.log"
JSON_LOG_USER_PATH = "/var/log/the/hci_user.log"


GOAPI_URI="goapi"
GOAPI_PORT="8086"

LINUX_VM_MON_URL = "http://{{ vip }}:3000/d/Kdh0OoSGz/1-windows_exporter-for-prometheus-dashboard-cn-v20201012?orgId=1&kiosk"
VM_MON_URL = "http://{{ vip }}:3000/d/Kdh0OoSGz/1-windows_exporter-for-prometheus-dashboard-cn-v20201012?orgId=1&kiosk"
HOST_MON_URL = "http://{{ vip }}:3000/d/thecloud/1-node-exporter-for-prometheus-dashboard-cn-v20201010?orgId=1&kiosk"

CEPH_URL = "http://{{ vip }}:3000/d/Kdh0OoSGz/1-windows_exporter-for-prometheus-dashboard-cn-v20201012?orgId=1&kiosk"
POOL_URL = "http://{{ vip }}:3000/d/thecloud/1-node-exporter-for-prometheus-dashboard-cn-v20201010?orgId=1&kiosk"

GRAFANA_AUTH_URI="https://admin:admin@{{ vip }}:3000"

METRICS_URI="http://mgr.api/metrics"
#METRICS_URI="http://*************:9283/metrics"
RROMETHEUS_ALERT_URI = "http://{{ vip }}:9090/api/v1/alerts"
RROMETHEUS_QUERY_URI = "http://{{ vip }}:9090/api/v1/query"
RROMETHEUS_QUERY_RANGE_URI = "http://{{ vip }}:9090/api/v1/query_range"


ALERTMANAGER_URI = "http://{{ vip }}:9094"

HEALTH_CHECK_OPTION = [
    {
        "key": "alert",
        "name": "平台告警状态",
        "weigth": 20
    },
    {
        "key": "CPU_status",
        "name": "CPU硬件状态",
        "weigth": 10
    },
    {
        "key": "RAM_status",
        "name": "内存硬件状态",
        "weigth": 10
    },
    {
        "key": "storage_status",
        "name": "存储空间状态",
        "weigth": 10
    },
    {
        "key": "network-device",
        "name": "物理网卡状态",
        "weigth": 10
    },
    {
        "key": "hostroute",
        "name": "主机路由",
        "weigth": 10
    },
    # maojj

#--------------------------------------
    # ct

    {
        "key": "license",
        "name": "平台授权状态",
        "weigth": 10
    },
    {
        "key": "compute_service",
        "name": "计算服务状态",
        "weigth": 10
    },
    {
        "key": "network_service",
        "name": "网络服务状态",
        "weigth": 10
    },
    {
        "key": "storage_service",
        "name": "存储服务状态",
        "weigth": 10
    },
    {
        "key": "vms_status",
        "name": "虚拟机运行状态",
        "weigth": 10
    },

    {
        "key": "vports_status",
        "name": "虚拟端口状态",
        "weigth": 10
    }
]


