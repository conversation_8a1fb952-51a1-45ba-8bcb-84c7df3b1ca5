---
- name: deploy local asyn
  hosts: managers
  vars:
    base_dir: "/etc/thestack/conf/local_asyn"
    python_dir: "/opt"
    repos:
      - name: "hci_asyn"
        git_repo: "http://************/hci/hci_asyn.git"
        version: "master"
      - name: "hci_api"
        git_repo: "http://************/hci/hci_api.git"
        version: "master"
      - name: "hci_db"
        git_repo: "http://************/hci/hci_db.git"
        version: "master"
    python_paths:
      - "/root/apps/hci/hci_api"
      - "/root/apps/hci/hci_db"
    run_command: "export PYTHONPATH={{ base_dir }}/hci_api:{{ base_dir }}/hci_db && cd {{ base_dir }}/hci_asyn && nohup python agent_runner.py agent --queue queue_{{ ansible_host }} > {{ base_dir }}/hci_asyn/agent.log 2>&1 &"
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install local asyn
      include_role:
        name: "../roles/local_asyn"
      when: is_remove == 'false'

    - name: uninstall local asyn
      block:
        - name: 停止 local-asyn 服务
          systemd:
            name: local-asyn
            state: stopped
            enabled: no
          ignore_errors: true

        - name: 删除 systemd 服务文件
          file:
            path: /etc/systemd/system/local-asyn.service
            state: absent
          ignore_errors: true

        - name: 重新加载 systemd
          systemd:
            daemon_reload: yes
          ignore_errors: true

        - name: 清理遗留进程
          shell: pkill -f "agent_runner.py"
          ignore_errors: true
